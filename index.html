<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coffee Shop Map - Powered by Google Places UI Kit & Places API (New)</title>

    <!-- Google Maps JavaScript API with Places UI Kit -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyC-aLrX3HmDxCjR3STYJ3Y4TUR4z12I_mI&libraries=places,geometry&callback=initMap">
    </script>


    <!-- Google Places UI Kit -->
    <script type="importmap">
    {
        "imports": {
            "@googlemaps/places": "https://unpkg.com/@googlemaps/places@1.0.0/dist/index.min.js"
        }
    }
    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Google Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a73e8 0%, #4285f4 100%);
            min-height: 100vh;
        }

        .container {
            display: flex;
            height: 100vh;
            position: relative;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 24px;
            width: 400px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            z-index: 1000;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }

        .controls.hidden {
            transform: translateX(-100%);
        }

        .controls h1 {
            color: #1a73e8;
            margin-bottom: 24px;
            font-size: 24px;
            text-align: center;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 12px;
            font-weight: 500;
        }

        .search-section {
            margin-bottom: 24px;
        }

        .search-section h3 {
            color: #202124;
            margin-bottom: 16px;
            font-size: 16px;
            font-weight: 500;
        }

        /* Google Places UI Kit Autocomplete */
        .autocomplete-container {
            position: relative;
            margin-bottom: 16px;
        }

        #placeAutocomplete {
            width: 100%;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #dadce0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
            background: white;
        }

        .search-input:focus {
            outline: none;
            border-color: #1a73e8;
            box-shadow: 0 1px 6px rgba(26, 115, 232, 0.3);
        }

        .search-btn {
            width: 100%;
            padding: 12px;
            background: #1a73e8;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin-top: 12px;
            transition: background-color 0.2s, box-shadow 0.2s;
        }

        .search-btn:hover {
            background: #1557b0;
            box-shadow: 0 2px 8px rgba(26, 115, 232, 0.4);
        }

        .search-btn:disabled {
            background: #f1f3f4;
            color: #5f6368;
            cursor: not-allowed;
            box-shadow: none;
        }

        .info-section {
            background: rgba(248, 249, 250, 0.9);
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e8eaed;
        }

        .info-section h4 {
            color: #202124;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .info-section p {
            color: #5f6368;
            font-size: 12px;
            line-height: 1.4;
        }

        .api-info {
            background: linear-gradient(135deg, #e8f0fe 0%, #f8f9fa 100%);
            border-left: 4px solid #1a73e8;
        }

        .stats {
            display: flex;
            justify-content: space-between;
            margin-top: 12px;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 18px;
            font-weight: 600;
            color: #1a73e8;
        }

        .stat-label {
            font-size: 10px;
            color: #5f6368;
            text-transform: uppercase;
            font-weight: 500;
        }

        #map {
            flex: 1;
            height: 100%;
            position: relative;
            min-height: 400px;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 24px 32px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            z-index: 2000;
            display: none;
            text-align: center;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f1f3f4;
            border-top: 3px solid #1a73e8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: #202124;
            font-size: 14px;
            font-weight: 500;
        }

        /* Places UI Kit Components Styling */
        gmp-place-overview {
            width: 100%;
            margin: 16px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        gmp-place-list {
            width: 100%;
            max-height: 400px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* Top Rated Coffee Shops Sidebar */
        .top-rated-sidebar {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 350px;
            max-height: 80vh;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            z-index: 1000;
            overflow: hidden;
            display: none;
            transition: transform 0.3s ease;
        }

        .top-rated-sidebar.show {
            display: block;
        }

        .top-rated-sidebar.hidden {
            transform: translateX(100%);
        }

        .sidebar-header {
            background: linear-gradient(45deg, #1a73e8, #4285f4);
            color: white;
            padding: 16px 20px;
            position: relative;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }

        .sidebar-header .close-btn {
            position: absolute;
            top: 16px;
            right: 16px;
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }

        .sidebar-header .close-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar-content {
            max-height: calc(80vh - 60px);
            overflow-y: auto;
            padding: 0;
        }

        .coffee-shop-item {
            padding: 16px 20px;
            border-bottom: 1px solid #e8eaed;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .coffee-shop-item:hover {
            background-color: #f8f9fa;
        }

        .coffee-shop-item:last-child {
            border-bottom: none;
        }

        .shop-name {
            font-weight: 500;
            color: #202124;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .shop-rating {
            color: #fbbc04;
            font-size: 12px;
            margin-bottom: 4px;
        }

        .shop-address {
            color: #5f6368;
            font-size: 11px;
            line-height: 1.3;
        }

        .shop-details {
            margin-top: 8px;
            font-size: 11px;
        }

        .shop-price {
            display: inline-block;
            background: #e8f5e8;
            color: #137333;
            padding: 2px 6px;
            border-radius: 4px;
            margin-right: 8px;
        }

        .shop-hours {
            color: #5f6368;
        }

        /* Toggle buttons for hidden panels */
        .toggle-controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            font-size: 18px;
            display: none;
        }

        .toggle-controls.show {
            display: block;
        }

        .toggle-sidebar {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: rgba(26, 115, 232, 0.9);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            font-size: 18px;
            display: none;
        }

        .toggle-sidebar.show {
            display: block;
        }

        .controls .hide-btn {
            position: absolute;
            top: 16px;
            right: 16px;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #5f6368;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }

        .controls .hide-btn:hover {
            background-color: #f1f3f4;
            color: #202124;
        }

        @media (max-width: 768px) {
            .controls {
                width: 100%;
                height: 100vh;
                position: absolute;
                top: 0;
                left: 0;
            }

            .top-rated-sidebar {
                width: calc(100% - 40px);
                right: 20px;
                left: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Toggle buttons for hidden panels -->
    <button class="toggle-controls" id="toggleControls">☰</button>
    <button class="toggle-sidebar" id="toggleSidebar">⭐</button>

    <div class="container">
        <!-- Controls Panel -->
        <div class="controls" id="controls">
            <button class="hide-btn" id="hideControls">&times;</button>

            <h1>☕ Coffee Shop Discovery</h1>

            <div class="search-section">
                <h3>🔍 Find Coffee Shops</h3>

                <!-- Google Places Autocomplete -->
                <div class="autocomplete-container">
                    <input type="text" id="placeAutocomplete" class="search-input" placeholder="Enter city or address (e.g., Seattle, WA)">
                </div>

                <!-- Alternative text input for manual search -->
                <div class="autocomplete-container" style="margin-top: 12px;">
                    <input type="text" id="manualLocationInput" class="search-input" placeholder="Or type location manually and click search">
                </div>

                <button class="search-btn" id="searchLocation">Search Coffee Shops</button>
                <button class="search-btn" id="loadNearby" style="background: #137333; margin-top: 8px;">Load Nearby Coffee Shops</button>
                <button class="search-btn" id="testSeattle" style="background: #ea4335; margin-top: 8px;">Test: Search Seattle</button>
                <button class="search-btn" id="testMarker" style="background: #ff6d01; margin-top: 8px;">🔴 Test: Add Marker</button>
            </div>

            <div class="info-section api-info">
                <h4>🚀 Powered by Google Places</h4>
                <p>This app uses <strong>Google Places UI Kit</strong> and <strong>Places API (New)</strong> for the most accurate and comprehensive coffee shop data.</p>
                <div class="stats">
                    <div class="stat">
                        <div class="stat-number" id="coffeeCount">0</div>
                        <div class="stat-label">Coffee Shops</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="avgRating">0.0</div>
                        <div class="stat-label">Avg Rating</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="topRated">0</div>
                        <div class="stat-label">Top Rated</div>
                    </div>
                </div>
            </div>

            <div class="info-section">
                <h4>💡 How to Use</h4>
                <p>• Use the <strong>autocomplete field</strong> - start typing and select from suggestions<br>
                • Or type in the <strong>manual input field</strong> and click "Search Coffee Shops"<br>
                • Click "Load Nearby" to find coffee shops around your current map view<br>
                • Click on <strong>coffee markers</strong> on the map for detailed information<br>
                • Use the top-rated sidebar to discover the best coffee spots</p>
            </div>
        </div>

        <!-- Map Container -->
        <div id="map"></div>
    </div>

    <!-- Top Rated Coffee Shops Sidebar -->
    <div class="top-rated-sidebar" id="topRatedSidebar">
        <div class="sidebar-header">
            <h3>⭐ Top Rated Coffee Shops</h3>
            <button class="close-btn" id="closeSidebar">&times;</button>
        </div>
        <div class="sidebar-content" id="sidebarContent">
            <p style="text-align: center; color: #5f6368; padding: 20px;">Search for coffee shops to see top rated places in the area</p>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div class="loading" id="loading">
        <div class="spinner"></div>
        <div class="loading-text">Finding amazing coffee shops...</div>
    </div>

    <!-- Initialize Google Maps when API is loaded -->
    <script>
        // Global variables
        let map;
        let service;
        let coffeeMarkers = [];
        let coffeeShopsData = [];
        let autocomplete;

        // Initialize the application when Google Maps API is loaded
        function initMap() {
            console.log('🚀 Initializing Google Maps with Places UI Kit...');

            try {
                // Check if Google Maps API is available
                if (typeof google === 'undefined' || !google.maps) {
                    throw new Error('Google Maps API not loaded');
                }

                // Initialize Google Map
                map = new google.maps.Map(document.getElementById('map'), {
                    center: { lat: 39.8283, lng: -98.5795 }, // Center of US
                    zoom: 4,
                    mapTypeId: google.maps.MapTypeId.ROADMAP,
                    styles: [
                        {
                            featureType: 'poi.business',
                            elementType: 'labels',
                            stylers: [{ visibility: 'off' }]
                        }
                    ]
                });

                // Initialize Places Service
                service = new google.maps.places.PlacesService(map);
                console.log('✅ Places service initialized:', !!service);

                // Wait a bit for the map to fully load before initializing UI Kit
                setTimeout(() => {
                    initializePlacesUIKit();
                }, 1000);

                // Set up event listeners
                setupEventListeners();

                console.log('✅ Map and Places services initialized successfully');

            } catch (error) {
                console.error('❌ Error initializing map:', error);
                alert('Error loading Google Maps. Please refresh the page and try again.');
            }
        }

        // Make initMap available globally for Google Maps API callback
        window.initMap = initMap;
    </script>

    <script>
        // Configuration - Using new API key for both Places UI Kit and Places API (New)
        const GOOGLE_API_KEY = 'AIzaSyC-aLrX3HmDxCjR3STYJ3Y4TUR4z12I_mI';

        // UI Elements
        const loading = document.getElementById('loading');
        const searchButton = document.getElementById('searchLocation');
        const loadNearbyButton = document.getElementById('loadNearby');
        const sidebar = document.getElementById('topRatedSidebar');
        const sidebarContent = document.getElementById('sidebarContent');
        const toggleControls = document.getElementById('toggleControls');
        const toggleSidebar = document.getElementById('toggleSidebar');
        const hideControls = document.getElementById('hideControls');
        const closeSidebar = document.getElementById('closeSidebar');
        const controls = document.getElementById('controls');

        // Stats elements
        const coffeeCountEl = document.getElementById('coffeeCount');
        const avgRatingEl = document.getElementById('avgRating');
        const topRatedEl = document.getElementById('topRated');

        // Initialize Google Places Autocomplete (standard implementation)
        function initializePlacesUIKit() {
            const autocompleteElement = document.getElementById('placeAutocomplete');

            if (autocompleteElement) {
                console.log('🔧 Setting up Google Places Autocomplete...');

                // Create standard Google Places Autocomplete
                const autocomplete = new google.maps.places.Autocomplete(autocompleteElement, {
                    types: ['(cities)'], // Restrict to cities and addresses
                    fields: ['place_id', 'geometry', 'name', 'formatted_address']
                });

                // Listen for place selection
                autocomplete.addListener('place_changed', () => {
                    const place = autocomplete.getPlace();
                    console.log('📍 Place selected:', place);

                    if (place.geometry && place.geometry.location) {
                        console.log('📍 Place location:', place.geometry.location.toString());

                        // Center map on selected location
                        map.setCenter(place.geometry.location);
                        map.setZoom(13);

                        // Search for coffee shops in this area
                        searchCoffeeShopsNearby(place.geometry.location);
                    } else {
                        console.warn('⚠️ No geometry found for selected place');
                        alert('Please select a place from the dropdown suggestions.');
                    }
                });

                console.log('✅ Google Places Autocomplete initialized');
            } else {
                console.warn('⚠️ Autocomplete element not found');
            }
        }

        // Set up event listeners
        function setupEventListeners() {
            // Search button - Now uses manual input field
            searchButton.addEventListener('click', () => {
                const manualInput = document.getElementById('manualLocationInput');
                const inputValue = manualInput.value?.trim();

                console.log('🔍 Search button clicked, manual input value:', inputValue);

                if (inputValue) {
                    showLoading(true);
                    // Use Google Geocoding to get coordinates from text input
                    const geocoder = new google.maps.Geocoder();
                    geocoder.geocode({ address: inputValue }, (results, status) => {
                        console.log('🌍 Geocoding result:', status, results);

                        if (status === 'OK' && results[0]) {
                            const location = results[0].geometry.location;
                            console.log('📍 Found location:', location.toString());
                            map.setCenter(location);
                            map.setZoom(13);
                            searchCoffeeShopsNearby(location);
                        } else {
                            showLoading(false);
                            console.error('❌ Geocoding failed:', status);
                            alert(`Location not found (${status}). Please try a different search term.`);
                        }
                    });
                } else {
                    alert('Please enter a location in the text field, or use the autocomplete dropdown above.');
                }
            });

            // Load nearby button
            loadNearbyButton.addEventListener('click', () => {
                const center = map.getCenter();
                console.log('🎯 Loading nearby coffee shops at:', center.toString());
                searchCoffeeShopsNearby(center);
            });

            // Test Seattle button
            const testSeattleButton = document.getElementById('testSeattle');
            testSeattleButton.addEventListener('click', () => {
                console.log('🧪 Testing with Seattle coordinates');
                const seattleLocation = new google.maps.LatLng(47.6062, -122.3321);
                map.setCenter(seattleLocation);
                map.setZoom(13);

                // Add a test marker first to verify marker system works
                const testMarker = new google.maps.Marker({
                    position: seattleLocation,
                    map: map,
                    title: 'Test Marker - Seattle Center',
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 15,
                        fillColor: '#FF0000',
                        fillOpacity: 1.0,
                        strokeColor: '#FFFFFF',
                        strokeWeight: 3
                    }
                });
                console.log('🔴 Test marker added at Seattle center');

                // Then search for coffee shops
                searchCoffeeShopsNearby(seattleLocation);
            });

            // Test Marker button
            const testMarkerButton = document.getElementById('testMarker');
            testMarkerButton.addEventListener('click', () => {
                console.log('🔴 Testing marker creation...');
                const center = map.getCenter();

                // Create a simple test marker
                const testMarker = new google.maps.Marker({
                    position: center,
                    map: map,
                    title: 'Test Marker',
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 20,
                        fillColor: '#FF0000',
                        fillOpacity: 1.0,
                        strokeColor: '#FFFFFF',
                        strokeWeight: 4
                    }
                });

                console.log('🔴 Test marker created at map center:', center.toString());
                alert('Test marker added at map center! Check the map.');
            });

            // UI toggle buttons
            hideControls.addEventListener('click', () => {
                controls.classList.add('hidden');
                toggleControls.classList.add('show');
            });

            toggleControls.addEventListener('click', () => {
                controls.classList.remove('hidden');
                toggleControls.classList.remove('show');
            });

            closeSidebar.addEventListener('click', () => {
                sidebar.classList.remove('show');
                toggleSidebar.classList.add('show');
            });

            toggleSidebar.addEventListener('click', () => {
                sidebar.classList.add('show');
                toggleSidebar.classList.remove('show');
            });

            // Add Enter key support for manual search input
            const manualInput = document.getElementById('manualLocationInput');
            if (manualInput) {
                manualInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        searchButton.click();
                    }
                });
            }
        }

        // Function to show/hide loading
        function showLoading(show) {
            loading.classList.toggle('show', show);
        }



        // Search for coffee shops near a location using Google Places API
        function searchCoffeeShopsNearby(location) {
            showLoading(true);
            clearMarkers();

            console.log('🔍 Searching for coffee shops near:', location);
            console.log('🔧 Places service available:', !!service);

            if (!service) {
                console.error('❌ Places service not initialized');
                alert('Places service not ready. Please wait a moment and try again.');
                showLoading(false);
                return;
            }

            // Create request for nearby search
            const request = {
                location: location,
                radius: 5000, // 5km radius
                type: 'cafe', // Fixed: should be string, not array
                keyword: 'coffee',
                openNow: false // Don't restrict to only open places
            };

            console.log('📋 Search request:', request);

            // Perform the nearby search
            service.nearbySearch(request, (results, status) => {
                console.log('📊 Places API response:', status, results ? results.length : 0, 'results');

                if (status === google.maps.places.PlacesServiceStatus.OK && results) {
                    console.log(`✅ Found ${results.length} potential coffee shops`);
                    console.log('🏪 Sample results:', results.slice(0, 3).map(r => ({ name: r.name, types: r.types })));

                    // Filter for coffee-related places
                    const coffeeShops = results.filter(place => isCoffeeRelated(place.name, place.types));

                    console.log(`☕ Filtered to ${coffeeShops.length} coffee shops`);

                    if (coffeeShops.length === 0) {
                        console.warn('⚠️ No coffee shops found after filtering');
                        alert('No coffee shops found in this area. Try searching in a different location or expanding the search radius.');
                        showLoading(false);
                        return;
                    }

                    // Sort by rating (highest first)
                    coffeeShops.sort((a, b) => (b.rating || 0) - (a.rating || 0));

                    // Store the data
                    coffeeShopsData = coffeeShops;

                    // Add markers to map
                    console.log(`🎯 Adding ${coffeeShops.length} markers to map...`);
                    coffeeShops.forEach((shop, index) => {
                        console.log(`📍 Processing shop ${index + 1}:`, shop.name, shop.geometry?.location);
                        addCoffeeShopMarker(shop);
                    });
                    console.log(`✅ Finished adding markers. Total markers: ${coffeeMarkers.length}`);

                    // Update sidebar and stats
                    updateSidebar(coffeeShops);
                    updateStats(coffeeShops);

                    // Show sidebar
                    sidebar.classList.add('show');
                    toggleSidebar.classList.remove('show');

                } else {
                    console.error('❌ Places search failed:', status);

                    // Try a fallback search with different parameters
                    if (status === google.maps.places.PlacesServiceStatus.ZERO_RESULTS) {
                        console.log('🔄 Trying fallback search with broader parameters...');
                        const fallbackRequest = {
                            location: location,
                            radius: 10000, // Increase radius to 10km
                            keyword: 'coffee shop starbucks cafe'
                        };

                        service.nearbySearch(fallbackRequest, (fallbackResults, fallbackStatus) => {
                            if (fallbackStatus === google.maps.places.PlacesServiceStatus.OK && fallbackResults && fallbackResults.length > 0) {
                                console.log(`✅ Fallback search found ${fallbackResults.length} results`);

                                const coffeeShops = fallbackResults.filter(place => isCoffeeRelated(place.name, place.types));

                                if (coffeeShops.length > 0) {
                                    coffeeShops.sort((a, b) => (b.rating || 0) - (a.rating || 0));
                                    coffeeShopsData = coffeeShops;

                                    coffeeShops.forEach(shop => {
                                        addCoffeeShopMarker(shop);
                                    });

                                    updateSidebar(coffeeShops);
                                    updateStats(coffeeShops);
                                    sidebar.classList.add('show');
                                    toggleSidebar.classList.remove('show');
                                    showLoading(false);
                                    return;
                                }
                            }

                            // If fallback also fails, show error
                            showFinalError(status);
                        });
                        return;
                    }

                    showFinalError(status);
                }

                showLoading(false);
            });
        }

        // Show final error message
        function showFinalError(status) {
            let errorMessage = 'Error searching for coffee shops. ';

            switch(status) {
                case google.maps.places.PlacesServiceStatus.ZERO_RESULTS:
                    errorMessage += 'No coffee shops found in this area. Try a different location.';
                    break;
                case google.maps.places.PlacesServiceStatus.OVER_QUERY_LIMIT:
                    errorMessage += 'Query limit exceeded. Please try again later.';
                    break;
                case google.maps.places.PlacesServiceStatus.REQUEST_DENIED:
                    errorMessage += 'Request denied. Please check API key permissions.';
                    break;
                case google.maps.places.PlacesServiceStatus.INVALID_REQUEST:
                    errorMessage += 'Invalid request parameters.';
                    break;
                default:
                    errorMessage += 'Please try again.';
            }

            alert(errorMessage);
            showLoading(false);
        }

        // Enhanced coffee shop filtering
        function isCoffeeRelated(name, types) {
            if (!name) return false;

            const coffeeKeywords = [
                'coffee', 'cafe', 'espresso', 'latte', 'cappuccino', 'roaster', 'roastery',
                'brew', 'bean', 'grind', 'barista', 'americano', 'macchiato', 'mocha',
                'starbucks', 'dunkin', 'peet', 'blue bottle', 'intelligentsia',
                'counter culture', 'ritual', 'philz', 'caribou', 'tim hortons',
                'costa', 'nero', 'lavazza', 'illy', 'folgers', 'maxwell house'
            ];

            const coffeeTypes = ['cafe', 'coffee_shop', 'bakery'];
            const strongExclusions = [
                'gas_station', 'convenience_store', 'supermarket', 'grocery_or_supermarket',
                'night_club', 'bar', 'liquor_store', 'pharmacy', 'hospital', 'bank',
                'atm', 'car_dealer', 'car_repair', 'clothing_store', 'electronics_store'
            ];

            const nameLower = name.toLowerCase();

            // Check for strong exclusions first
            if (types && types.some(type => strongExclusions.includes(type))) {
                return false;
            }

            // Check for coffee-related keywords in name
            const hasKeyword = coffeeKeywords.some(keyword => nameLower.includes(keyword));

            // Check for coffee-related types
            const hasType = types && types.some(type => coffeeTypes.includes(type));

            return hasKeyword || hasType;
        }

        // Clear all markers from the map
        function clearMarkers() {
            coffeeMarkers.forEach(marker => {
                marker.setMap(null);
            });
            coffeeMarkers = [];
        }

        // Add a coffee shop marker to the map
        function addCoffeeShopMarker(place) {
            console.log('📍 Adding marker for:', place.name, place.geometry?.location);

            if (!place.geometry || !place.geometry.location) {
                console.error('❌ No geometry/location for place:', place.name);
                return;
            }

            const position = {
                lat: place.geometry.location.lat(),
                lng: place.geometry.location.lng()
            };

            console.log('📍 Marker position:', position);
            console.log('🗺️ Map object:', !!map);

            // Create marker with simple, reliable approach first
            const marker = new google.maps.Marker({
                position: position,
                map: map,
                title: place.name,
                // Start with default marker to ensure visibility
                icon: {
                    path: google.maps.SymbolPath.CIRCLE,
                    scale: 12,
                    fillColor: '#8B4513', // Coffee brown
                    fillOpacity: 1.0,
                    strokeColor: '#FFFFFF',
                    strokeWeight: 3
                },
                animation: google.maps.Animation.DROP,
                zIndex: 1000 // Ensure marker appears on top
            });

            // Try to set coffee icon after marker is created
            setTimeout(() => {
                try {
                    marker.setIcon({
                        url: 'https://maps.google.com/mapfiles/ms/icons/coffee.png',
                        scaledSize: new google.maps.Size(40, 40),
                        origin: new google.maps.Point(0, 0),
                        anchor: new google.maps.Point(20, 40)
                    });
                    console.log('☕ Coffee icon applied to marker');
                } catch (error) {
                    console.warn('⚠️ Could not apply coffee icon, using default:', error);
                }
            }, 100);

            console.log('✅ Marker created and added to map at:', position);

            // Create info window content
            const rating = place.rating || 'No rating';
            const priceLevel = place.price_level ? '$'.repeat(place.price_level) : 'Price not available';
            const status = place.opening_hours?.open_now ?
                '<span style="color: #137333;">Open now</span>' :
                place.opening_hours?.open_now === false ?
                '<span style="color: #d93025;">Closed</span>' :
                'Hours unknown';

            const infoContent = `
                <div style="max-width: 300px; font-family: 'Google Sans', sans-serif;">
                    <h3 style="color: #1a73e8; margin: 0 0 8px 0;">☕ ${place.name}</h3>
                    <p style="margin: 4px 0;"><strong>Rating:</strong> <span style="color: #fbbc04;">${rating}⭐</span> (${place.user_ratings_total || 0} reviews)</p>
                    <p style="margin: 4px 0;"><strong>Price Level:</strong> ${priceLevel}</p>
                    <p style="margin: 4px 0;"><strong>Status:</strong> ${status}</p>
                    <p style="margin: 4px 0; color: #5f6368; font-size: 12px;">${place.vicinity || place.formatted_address || 'Address not available'}</p>
                    <p style="margin: 8px 0 0 0; font-size: 11px; color: #5f6368;">Powered by Google Places</p>
                </div>
            `;

            const infoWindow = new google.maps.InfoWindow({
                content: infoContent
            });

            // Add click listener
            marker.addListener('click', () => {
                // Close any open info windows
                coffeeMarkers.forEach(m => {
                    if (m.infoWindow) {
                        m.infoWindow.close();
                    }
                });

                infoWindow.open(map, marker);
            });

            // Store reference to info window
            marker.infoWindow = infoWindow;

            // Add to markers array
            coffeeMarkers.push(marker);
        }

        // Update the sidebar with top coffee shops
        function updateSidebar(coffeeShops) {
            sidebarContent.innerHTML = '';

            const topShops = coffeeShops.slice(0, 10); // Show top 10

            topShops.forEach((shop, index) => {
                const shopElement = document.createElement('div');
                shopElement.className = 'coffee-shop-item';

                const rating = shop.rating || 'No rating';
                const priceLevel = shop.price_level ? '$'.repeat(shop.price_level) : '';
                const status = shop.opening_hours?.open_now ? 'Open now' :
                              shop.opening_hours?.open_now === false ? 'Closed' : '';

                shopElement.innerHTML = `
                    <div class="shop-name">${index + 1}. ${shop.name}</div>
                    <div class="shop-rating">⭐ ${rating} (${shop.user_ratings_total || 0} reviews)</div>
                    <div class="shop-address">${shop.vicinity || shop.formatted_address || 'Address not available'}</div>
                    <div class="shop-details">
                        ${priceLevel ? `<span class="shop-price">${priceLevel}</span>` : ''}
                        ${status ? `<span class="shop-hours">${status}</span>` : ''}
                    </div>
                `;

                // Add click handler to center map and open info window
                shopElement.addEventListener('click', () => {
                    const position = {
                        lat: shop.geometry.location.lat(),
                        lng: shop.geometry.location.lng()
                    };

                    // Center map on this coffee shop
                    map.setCenter(position);
                    map.setZoom(16);

                    // Find and open the corresponding marker info window
                    const marker = coffeeMarkers.find(m => {
                        const markerPos = m.getPosition();
                        return Math.abs(markerPos.lat() - position.lat) < 0.0001 &&
                               Math.abs(markerPos.lng() - position.lng) < 0.0001;
                    });

                    if (marker && marker.infoWindow) {
                        // Close all other info windows
                        coffeeMarkers.forEach(m => {
                            if (m.infoWindow) {
                                m.infoWindow.close();
                            }
                        });

                        marker.infoWindow.open(map, marker);
                    }
                });

                sidebarContent.appendChild(shopElement);
            });
        }

        // Update statistics
        function updateStats(coffeeShops) {
            const count = coffeeShops.length;
            const avgRating = count > 0 ?
                (coffeeShops.reduce((sum, shop) => sum + (shop.rating || 0), 0) / count).toFixed(1) :
                '0.0';
            const topRated = coffeeShops.filter(shop => shop.rating >= 4.5).length;

            coffeeCountEl.textContent = count;
            avgRatingEl.textContent = avgRating;
            topRatedEl.textContent = topRated;
        }

        // Initialize the app when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Coffee Shop Map with Google Places UI Kit & Places API (New) ready!');
            console.log('💡 Try searching for coffee-famous cities like Seattle, Portland, or San Francisco');
            console.log('🔧 Debug: DOM loaded, Google available:', typeof google !== 'undefined');

            // Add a small delay to check if Google Maps loads
            setTimeout(() => {
                console.log('🔧 Debug: Google Maps available after delay:', typeof google !== 'undefined' && !!google.maps);
                console.log('🔧 Debug: Map initialized:', !!map);
                console.log('🔧 Debug: Service initialized:', !!service);
            }, 2000);
        });
    </script>
</body>
</html>
